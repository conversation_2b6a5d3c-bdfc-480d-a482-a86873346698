use serde::Serialize;
use std::path::Path;
use tauri::Emitter;

use crate::common::{
    get_video_info_internal, is_cancelled, kill_ffmpeg_process, mark_ffmpeg_finished,
    mark_ffmpeg_running, reset_cancelled,
};
use crate::config;

#[derive(Clone, Serialize)]
struct Payload {
    pct: f32,
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
pub async fn adjust_volume(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    volume: f32,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }
    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取文件信息来判断是视频还是音频
    let video_info = get_video_info_internal(&input_path).await?;
    let is_audio_only = video_info.video_codec.is_empty() && !video_info.audio_codec.is_empty();

    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算
    println!("开始调整音量: {} -> {}", input_path, output_path);
    println!("音量调整值: {}", volume);

    let mut ffmpeg;
    if is_audio_only {
        // 对于纯音频文件，只处理音频流
        ffmpeg = config::create_ffmpeg_command()?
            .args([
                "-i",
                &input_path,
                "-af",
                &format!("volume={}", volume),
                "-y",
                &output_path,
            ])
            .spawn()
            .map_err(|e| {
                println!("[FFmpeg spawn error] {e:?}");
                format!("FFmpeg启动失败: {e}")
            })?;
    } else {
        // 对于视频文件，保持视频流不变
        ffmpeg = config::create_ffmpeg_command()?
            .args([
                "-i",
                &input_path,
                "-af",
                &format!("volume={}", volume),
                "-c:v",
                "copy",
                "-y",
                &output_path,
            ])
            .spawn()
            .map_err(|e| {
                println!("[FFmpeg spawn error] {e:?}");
                format!("FFmpeg启动失败: {e}")
            })?;
    }

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    window.emit("AUDIO_PROGRESS", Payload { pct: 0.0 }).unwrap();
    let mut last_pct = 0.0f32;
    let mut cancelled = false;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("音量调整处理已取消");
            kill_ffmpeg_process();
            cancelled = true;
            break;
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("AUDIO_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    if cancelled || is_cancelled() {
        return Err("处理已取消".to_string());
    }
    println!("✅ 音量调整完成！");
    window
        .emit("AUDIO_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

#[tauri::command]
pub async fn mute_audio(
    window: tauri::Window,
    input_path: String,
    output_path: String,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }
    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算
    println!("开始静音: {} -> {}", input_path, output_path);

    // 对于视频文件，移除音频流
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args(["-i", &input_path, "-an", "-c:v", "copy", "-y", &output_path])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    window.emit("AUDIO_PROGRESS", Payload { pct: 0.0 }).unwrap();
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("静音处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("AUDIO_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 静音完成！");
    window
        .emit("AUDIO_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

#[tauri::command]
pub async fn extract_audio(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    format: String,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }
    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }
    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算
    println!(
        "开始提取音频: {} -> {} 格式: {}",
        input_path, output_path, format
    );
    let mut args = vec!["-i", &input_path];
    match format.as_str() {
        "mp3" => args.extend(["-vn", "-acodec", "libmp3lame", "-y", &output_path]),
        "wav" => args.extend(["-vn", "-acodec", "pcm_s16le", "-y", &output_path]),
        "aac" => args.extend(["-vn", "-acodec", "aac", "-y", &output_path]),
        "flac" => args.extend(["-vn", "-acodec", "flac", "-y", &output_path]),
        "m4a" => args.extend(["-vn", "-acodec", "aac", "-y", &output_path]),
        "ogg" => args.extend(["-vn", "-acodec", "libvorbis", "-y", &output_path]),
        _ => return Err("不支持的音频格式".to_string()),
    }
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args(args)
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    window.emit("AUDIO_PROGRESS", Payload { pct: 0.0 }).unwrap();
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("音频提取处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("AUDIO_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 音频提取完成！");
    window
        .emit("AUDIO_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

#[tauri::command]
pub async fn add_bgmusic(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    bgm_path: String,
    loop_bgm: bool,
    bgm_volume: Option<f32>,
) -> Result<(), String> {
    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }
    if !Path::new(&bgm_path).exists() {
        return Err("背景音乐文件不存在".to_string());
    }
    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }
    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算
    println!(
        "开始添加背景音乐: {} -> {} 背景音乐: {}",
        input_path, output_path, bgm_path
    );
    println!(
        "循环播放: {}, 音量: {}",
        loop_bgm,
        bgm_volume.unwrap_or(0.5)
    );

    let volume = bgm_volume.unwrap_or(0.5);
    let mut args = vec!["-i", &input_path, "-i", &bgm_path];
    let filter_complex;

    if loop_bgm {
        // 循环播放背景音乐
        filter_complex = format!(
            "[1:a]aloop=loop=-1:size=2e+09,volume={}[a];[0:a][a]amix=inputs=2:duration=first",
            volume
        );
    } else {
        // 只播放一次背景音乐
        filter_complex = format!(
            "[1:a]volume={}[a];[0:a][a]amix=inputs=2:duration=first",
            volume
        );
    }

    println!("Filter complex: {}", filter_complex);

    args.extend([
        "-filter_complex",
        &filter_complex,
        "-c:v",
        "copy",
        "-y",
        &output_path,
    ]);
    let mut ffmpeg = config::create_ffmpeg_command()?
        .args(args)
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    window.emit("AUDIO_PROGRESS", Payload { pct: 0.0 }).unwrap();
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("背景音乐添加处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("AUDIO_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    // 等待FFmpeg进程完成
    let status = ffmpeg.wait().map_err(|e| {
        println!("[FFmpeg wait error] {e:?}");
        format!("FFmpeg等待失败: {e}")
    })?;

    if !status.success() {
        return Err("背景音乐添加失败".to_string());
    }

    println!("✅ 背景音乐添加完成！");
    window
        .emit("AUDIO_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

#[tauri::command]
pub async fn audio_replace(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    audio_path: String,
    loop_audio: bool,
    audio_volume: Option<f32>,
) -> Result<(), String> {
    reset_cancelled();
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }
    if !Path::new(&audio_path).exists() {
        return Err("音频文件不存在".to_string());
    }
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64;
    println!("开始替换音频: {} -> {}", input_path, output_path);
    println!("新音频文件: {}", audio_path);
    let volume = audio_volume.unwrap_or(1.0);
    println!(
        "循环播放: {}, 音量: {}",
        loop_audio,
        audio_volume.unwrap_or(1.0)
    );
    mark_ffmpeg_running();
    window.emit("AUDIO_PROGRESS", Payload { pct: 0.0 }).unwrap();

    let mut args = vec!["-i", &input_path, "-i", &audio_path];

    let filter_complex;
    if loop_audio {
        // 循环播放音频，以视频长度为准
        filter_complex = format!(
            "[1:a]aloop=loop=-1:size=2e+09,volume={}[looped_audio];[looped_audio]atrim=duration={}[a]",
            volume,
            video_info.duration
        );
        args.extend([
            "-filter_complex",
            &filter_complex,
            "-map",
            "0:v",
            "-map",
            "[a]",
            "-c:v",
            "copy",
            "-y",
            &output_path,
        ]);
    } else {
        // 不循环，直接替换音频，以视频长度为准
        filter_complex = format!(
            "[1:a]volume={}[vol_audio];[vol_audio]atrim=duration={}[a]",
            volume, video_info.duration
        );
        args.extend([
            "-filter_complex",
            &filter_complex,
            "-map",
            "0:v",
            "-map",
            "[a]",
            "-c:v",
            "copy",
            "-y",
            &output_path,
        ]);
    }

    println!("Filter complex: {}", filter_complex);
    println!("视频时长: {} 秒", video_info.duration);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args(args)
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("音频替换处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }
        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("AUDIO_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }
    mark_ffmpeg_finished();
    println!("✅ 音频替换完成！");
    window
        .emit("AUDIO_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}
