use serde::Serialize;
use std::path::Path;
use tauri::Emitter;

use crate::common::{
    get_video_info_internal, is_cancelled, kill_ffmpeg_process, mark_ffmpeg_finished,
    mark_ffmpeg_running, reset_cancelled,
};
use crate::config;

#[derive(Clone, Serialize)]
struct Payload {
    pct: f32,
}

// 反转视频
#[tauri::command]
pub async fn reverse_video(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    keep_audio: bool,
) -> Result<(), String> {
    println!("reverse_video called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  keep_audio: {}", keep_audio);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    println!("开始处理视频: {} -> {}", input_path, output_path);
    println!("反转视频播放顺序，保留音频: {}", keep_audio);

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    let ffmpeg_args = if keep_audio {
        vec![
            "-i",
            &input_path,
            "-filter_complex",
            "[0:v]reverse[v];[0:a]areverse[a]",
            "-map",
            "[v]",
            "-map",
            "[a]",
            "-c:v",
            "libx264",
            "-c:a",
            "aac",
            "-y",
            &output_path,
        ]
    } else {
        vec![
            "-i",
            &input_path,
            "-vf",
            "reverse",
            "-an", // 不包含音频
            "-c:v",
            "libx264",
            "-y",
            &output_path,
        ]
    };

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args(ffmpeg_args)
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("EFFECT_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("EFFECT_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 视频处理完成！");
    window
        .emit("EFFECT_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 调整播放速度
#[tauri::command]
pub async fn adjust_speed(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    speed: f32,
) -> Result<(), String> {
    println!("adjust_speed called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  speed: {}", speed);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    println!("开始处理视频: {} -> {}", input_path, output_path);
    println!("调整播放速度为: {}x", speed);

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    // atempo滤镜只支持0.5-2.0倍速，对于超出范围的速度需要链式处理
    let audio_filter = if speed >= 0.5 && speed <= 2.0 {
        format!("atempo={}", speed)
    } else if speed < 0.5 {
        // 对于小于0.5的速度，需要多次应用atempo
        let mut filter = String::new();
        let mut current_speed = speed;
        let mut first = true;

        while current_speed < 0.5 {
            if !first {
                filter.push(',');
            }
            filter.push_str("atempo=0.5");
            current_speed /= 0.5;
            first = false;
        }

        if current_speed != 1.0 {
            if !first {
                filter.push(',');
            }
            filter.push_str(&format!("atempo={}", current_speed));
        }

        filter
    } else {
        // 对于大于2.0的速度，需要多次应用atempo
        let mut filter = String::new();
        let mut current_speed = speed;
        let mut first = true;

        while current_speed > 2.0 {
            if !first {
                filter.push(',');
            }
            filter.push_str("atempo=2.0");
            current_speed /= 2.0;
            first = false;
        }

        if current_speed != 1.0 {
            if !first {
                filter.push(',');
            }
            filter.push_str(&format!("atempo={}", current_speed));
        }

        filter
    };

    println!("生成的音频滤镜: {}", audio_filter);
    println!("视频PTS调整: {}", 1.0 / speed);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-filter_complex",
            &format!(
                "[0:v]setpts={}*PTS[v];[0:a]{}[a]",
                1.0 / speed,
                audio_filter
            ),
            "-map",
            "[v]",
            "-map",
            "[a]",
            "-c:v",
            "libx264",
            "-c:a",
            "aac",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("EFFECT_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("EFFECT_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 视频处理完成！");
    window
        .emit("EFFECT_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 淡入效果
#[tauri::command]
pub async fn fade_in(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    duration: f32,
) -> Result<(), String> {
    println!("fade_in called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  duration: {}", duration);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    println!("开始处理视频: {} -> {}", input_path, output_path);
    println!("添加淡入效果，时长: {}秒", duration);

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &format!("fade=t=in:st=0:d={}", duration),
            "-af",
            &format!("afade=t=in:st=0:d={}", duration),
            "-c:v",
            "libx264",
            "-c:a",
            "aac",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("EFFECT_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("EFFECT_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 视频处理完成！");
    window
        .emit("EFFECT_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 淡出效果
#[tauri::command]
pub async fn fade_out(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    duration: f32,
) -> Result<(), String> {
    println!("fade_out called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  duration: {}", duration);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    // 获取视频信息来计算进度和淡出开始时间
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算
    let fade_start_time = video_info.duration - duration as f64;

    println!("开始处理视频: {} -> {}", input_path, output_path);
    println!(
        "添加淡出效果，时长: {}秒，开始时间: {}秒",
        duration, fade_start_time
    );

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &format!("fade=t=out:st={}:d={}", fade_start_time, duration),
            "-af",
            &format!("afade=t=out:st={}:d={}", fade_start_time, duration),
            "-c:v",
            "libx264",
            "-c:a",
            "aac",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("EFFECT_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("EFFECT_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 视频处理完成！");
    window
        .emit("EFFECT_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 模糊效果
#[tauri::command]
pub async fn blur_video(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    intensity: i32,
) -> Result<(), String> {
    println!("blur_video called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  intensity: {}", intensity);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    println!("开始处理视频: {} -> {}", input_path, output_path);
    println!("应用模糊效果，强度: {}", intensity);

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &format!("boxblur=luma_radius={}:luma_power=1", intensity),
            "-c:v",
            "libx264",
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("EFFECT_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("EFFECT_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 视频处理完成！");
    window
        .emit("EFFECT_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 锐化效果
#[tauri::command]
pub async fn sharpen_video(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    intensity: f32,
) -> Result<(), String> {
    println!("sharpen_video called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  intensity: {}", intensity);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    println!("开始处理视频: {} -> {}", input_path, output_path);
    println!("应用锐化效果，强度: {}", intensity);

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &format!("unsharp=3:3:{}:3:3:0", intensity),
            "-c:v",
            "libx264",
            "-c:a",
            "copy",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("EFFECT_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频处理已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("EFFECT_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 视频处理完成！");
    window
        .emit("EFFECT_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}
