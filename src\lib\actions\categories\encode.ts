// 编码类动作定�?

import {
  CategoryIds,
  ActionIds,
  VideoFormats,
  ResolutionPresets,
  BitratePresets,
} from "../../constants";
import type { Action } from "../../types/action";
import { applyActionMapping } from "../action-mappings";

// 编码类动作定�?
const rawEncodeActions = [
  {
    id: ActionIds.CONVERT_FORMAT,
    nameKey: "actions.convert_format.name",
    descriptionKey: "actions.convert_format.description",
    categoryId: CategoryIds.ENCODE,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: false,
    order: 10,
    params: [
      {
        key: "format",
        type: "select",
        nameKey: "actions.convert_format.params.format",
        required: true,
        defaultValue: VideoFormats.MP4,
        options: [
          { value: VideoFormats.MP4, labelKey: "formats.video.mp4" },
          { value: VideoFormats.AVI, labelKey: "formats.video.avi" },
          { value: VideoFormats.MOV, labelKey: "formats.video.mov" },
          { value: VideoFormats.MKV, labelKey: "formats.video.mkv" },
          { value: VideoFormats.WEBM, labelKey: "formats.video.webm" },
        ],
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      const validFormats = Object.values(VideoFormats);
      if (!validFormats.includes(params.format)) {
        errors.push("actions.convert_format.errors.invalid_format");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.COMPRESS_VIDEO,
    nameKey: "actions.compress_video.name",
    descriptionKey: "actions.compress_video.description",
    categoryId: CategoryIds.ENCODE,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: false,
    order: 20,
    params: [
      {
        key: "compressionLevel",
        type: "range",
        nameKey: "actions.compress_video.params.compressionLevel",
        required: true,
        defaultValue: 5,
        min: 1,
        max: 10,
        step: 1,
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      if (params.compressionLevel < 1 || params.compressionLevel > 10) {
        errors.push("actions.compress_video.errors.invalid_level");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.ADJUST_BITRATE,
    nameKey: "actions.adjust_bitrate.name",
    descriptionKey: "actions.adjust_bitrate.description",
    categoryId: CategoryIds.ENCODE,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: false,
    order: 30,
    params: [
      {
        key: "bitrateType",
        type: "select",
        nameKey: "actions.adjust_bitrate.params.bitrateType",
        required: true,
        defaultValue: BitratePresets.MEDIUM,
        options: [
          { value: BitratePresets.LOW, labelKey: "bitrates.low" },
          { value: BitratePresets.MEDIUM, labelKey: "bitrates.medium" },
          { value: BitratePresets.HIGH, labelKey: "bitrates.high" },
          { value: BitratePresets.ULTRA, labelKey: "bitrates.ultra" },
          { value: BitratePresets.CUSTOM, labelKey: "bitrates.custom" },
        ],
      },
      {
        key: "customBitrate",
        type: "number",
        nameKey: "actions.adjust_bitrate.params.customBitrate",
        required: false,
        defaultValue: 2000,
        min: 100,
        max: 50000,
        dependsOn: "bitrateType",
        dependsOnValue: BitratePresets.CUSTOM,
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      if (params.bitrateType === BitratePresets.CUSTOM) {
        if (params.customBitrate < 100 || params.customBitrate > 50000) {
          errors.push("actions.adjust_bitrate.errors.invalid_custom_bitrate");
        }
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.ADJUST_RESOLUTION,
    nameKey: "actions.adjust_resolution.name",
    descriptionKey: "actions.adjust_resolution.description",
    categoryId: CategoryIds.ENCODE,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 40,
    params: [
      {
        key: "resolutionType",
        type: "select",
        nameKey: "actions.adjust_resolution.params.resolutionType",
        required: true,
        defaultValue: "1080p",
        options: [
          { value: "480p", labelKey: "resolutions.480p" },
          { value: "720p", labelKey: "resolutions.720p" },
          { value: "1080p", labelKey: "resolutions.1080p" },
          { value: "1440p", labelKey: "resolutions.1440p" },
          { value: "4K", labelKey: "resolutions.4k" },
          { value: "custom", labelKey: "resolutions.custom" },
        ],
      },
      {
        key: "customWidth",
        type: "number",
        nameKey: "actions.adjust_resolution.params.customWidth",
        required: false,
        defaultValue: 1920,
        min: 64,
        max: 7680,
      },
      {
        key: "customHeight",
        type: "number",
        nameKey: "actions.adjust_resolution.params.customHeight",
        required: false,
        defaultValue: 1080,
        min: 64,
        max: 4320,
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      if (params.resolutionType === "custom") {
        if (params.customWidth < 64 || params.customWidth > 7680) {
          errors.push("actions.adjust_resolution.errors.invalid_width");
        }
        if (params.customHeight < 64 || params.customHeight > 4320) {
          errors.push("actions.adjust_resolution.errors.invalid_height");
        }
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
];

// 应用映射并导�?
export const encodeActions: Action[] = rawEncodeActions.map(applyActionMapping);
