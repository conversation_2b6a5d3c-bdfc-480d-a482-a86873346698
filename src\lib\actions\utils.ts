// 动作系统工具函数

import type {
  ActionParams,
  ActionParamDefinition,
  ValidationResult,
} from "../types/action";

/**
 * 验证动作参数
 */
export function validateActionParams(
  params: ActionParams,
  paramDefinitions: ActionParamDefinition[]
): ValidationResult {
  const errors: string[] = [];

  for (const definition of paramDefinitions) {
    const value = params[definition.key];

    // 检查必需参数
    if (
      definition.required &&
      (value === undefined || value === null || value === "")
    ) {
      errors.push(`Parameter ${definition.key} is required`);
      continue;
    }

    // 如果值为空且不是必需的，跳过验证
    if (value === undefined || value === null || value === "") {
      continue;
    }

    // 类型验证
    switch (definition.type) {
      case "number":
      case "duration":
        // 对于 duration 类型，允许字符串输入并尝试转换为数字
        const numValue =
          definition.type === "duration"
            ? parseFloat(value)
            : typeof value === "number"
            ? value
            : NaN;
        if (isNaN(numValue)) {
          errors.push(`Parameter ${definition.key} must be a valid number`);
        } else {
          if (definition.min !== undefined && numValue < definition.min) {
            errors.push(
              `Parameter ${definition.key} must be >= ${definition.min}`
            );
          }
          if (definition.max !== undefined && numValue > definition.max) {
            errors.push(
              `Parameter ${definition.key} must be <= ${definition.max}`
            );
          }
        }
        break;

      case "string":
        if (typeof value !== "string") {
          errors.push(`Parameter ${definition.key} must be a string`);
        }
        break;

      case "boolean":
        if (typeof value !== "boolean") {
          errors.push(`Parameter ${definition.key} must be a boolean`);
        }
        break;

      case "file":
      case "image-file":
        if (typeof value !== "string" || value.trim() === "") {
          errors.push(`Parameter ${definition.key} must be a valid file path`);
        }
        break;

      case "select":
        if (definition.options) {
          const validValues = definition.options.map((opt) => opt.value);
          if (!validValues.includes(value)) {
            errors.push(
              `Parameter ${definition.key} must be one of: ${validValues.join(
                ", "
              )}`
            );
          }
        }
        break;
    }

    // 自定义验证
    if (definition.validation) {
      const result = definition.validation(value);
      if (typeof result === "string") {
        errors.push(result);
      } else if (result === false) {
        errors.push(`Parameter ${definition.key} validation failed`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 获取参数的默认值
 */
export function getDefaultParamValue(definition: ActionParamDefinition): any {
  if (definition.defaultValue !== undefined) {
    return definition.defaultValue;
  }

  switch (definition.type) {
    case "number":
    case "range":
      return definition.min ?? 0;
    case "string":
    case "file":
    case "image-file":
    case "audio-file":
    case "video-file":
    case "color":
      return "";
    case "boolean":
      return false;
    case "select":
      return definition.options?.[0]?.value ?? "";
    case "duration":
      return 0;
    case "resolution":
      return { width: 1920, height: 1080 };
    case "position":
      return { x: 0, y: 0 };
    default:
      return null;
  }
}

/**
 * 创建动作参数的默认值对象
 */
export function createDefaultParams(
  paramDefinitions: ActionParamDefinition[]
): ActionParams {
  const params: ActionParams = {};

  for (const definition of paramDefinitions) {
    params[definition.key] = getDefaultParamValue(definition);
  }

  return params;
}
