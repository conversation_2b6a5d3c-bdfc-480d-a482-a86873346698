{"app": {"title": "VideoIDE", "description": "Video Processing Tool"}, "common": {"add": "Add", "remove": "Remove", "edit": "Edit", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "close": "Close", "search": "Search", "preview": "Preview", "settings": "Settings", "loading": "Loading...", "processing": "Processing...", "completed": "Completed", "error": "Error", "warning": "Warning", "info": "Info", "success": "Success", "browse": "Browse", "select": "Select", "upload": "Upload", "download": "Download", "import": "Import", "export": "Export", "clear": "Clear", "reset": "Reset", "apply": "Apply", "undo": "Undo", "redo": "Redo", "copy": "Copy", "paste": "Paste", "cut": "Cut", "move_up": "Move Up", "move_down": "Move Down", "expand": "Expand", "collapse": "Collapse", "sort": "Sort", "filter": "Filter", "refresh": "Refresh", "help": "Help", "about": "About"}, "categories": {"trim": {"name": "<PERSON><PERSON>", "description": "Video and audio trimming operations"}, "color": {"name": "Color Adjustment", "description": "Video color, brightness, contrast adjustments"}, "audio": {"name": "Audio Processing", "description": "Audio-related processing operations"}, "transform": {"name": "Transform", "description": "Video rotation, scaling, cropping transformations"}, "effect": {"name": "Effects", "description": "Various visual effects and filters"}, "watermark": {"name": "Watermark", "description": "Add text or image watermarks"}, "encode": {"name": "Encoding", "description": "Video format conversion and encoding settings"}, "image": {"name": "Image", "description": "Conversion operations between images and videos"}, "filter": {"name": "Filter", "description": "Various video filter effects"}}, "actions": {"trim_start": {"name": "<PERSON><PERSON>", "description": "Remove specified duration from video beginning", "params": {"startDuration": "Start Duration (seconds)"}, "errors": {"negative_duration": "Duration cannot be negative", "invalid_number": "Please enter a valid number"}}, "trim_end": {"name": "<PERSON>m <PERSON>", "description": "Remove specified duration from video end", "params": {"endTime": "End Time (seconds)"}, "errors": {"negative_duration": "Duration cannot be negative", "invalid_number": "Please enter a valid number"}}, "trim_segment": {"name": "Extract Multiple Segments", "description": "Extract and merge multiple video segments", "params": {"segments": "Time Segments"}, "errors": {"no_segments": "Please add at least one time segment"}}, "exclude_segment": {"name": "Exclude Multiple Segments", "description": "Exclude specified segments and merge remaining parts", "params": {"segments": "Segments to Exclude"}, "errors": {"no_segments": "Please add at least one segment to exclude"}}, "adjust_brightness": {"name": "Adjust Brightness", "description": "Adjust video brightness", "params": {"brightness": "Brightness Value"}, "errors": {"invalid_range": "Brightness value must be between -1 and 1", "invalid_number": "Please enter a valid number"}}, "adjust_contrast": {"name": "Adjust Contrast", "description": "Adjust video contrast", "params": {"contrast": "Contrast Value"}, "errors": {"invalid_range": "Contrast value must be between 0 and 3"}}, "adjust_saturation": {"name": "Adjust Saturation", "description": "Adjust video saturation", "params": {"saturation": "Saturation Value"}, "errors": {"invalid_range": "Saturation value must be between 0 and 3"}}, "adjust_hue": {"name": "<PERSON>ju<PERSON>", "description": "Adjust video hue", "params": {"hue": "Hue Value"}, "errors": {"invalid_range": "Hue value must be between -180 and 180"}}, "adjust_gamma": {"name": "Adjust Gamma", "description": "Adjust video gamma", "params": {"gamma": "Gamma Value"}, "errors": {"invalid_range": "Gamma value must be between 0.1 and 3.0"}}, "white_balance": {"name": "White Balance", "description": "Adjust video white balance", "params": {"temperature": "Temperature Value"}, "errors": {"invalid_range": "Temperature value must be between 2000 and 11000"}}, "adjust_volume": {"name": "Adjust Volume", "description": "Adjust audio volume level", "params": {"volume": "Volume Multiplier"}, "errors": {"invalid_range": "Volume multiplier must be between 0 and 3", "invalid_number": "Please enter a valid number"}}, "extract_audio": {"name": "Extract Audio", "description": "Extract audio from video", "params": {"format": "Audio Format"}, "errors": {"invalid_format": "Unsupported audio format"}}, "add_background_music": {"name": "Add Background Music", "description": "Add background music to video", "params": {"audioFile": "Audio File", "loopBgm": "Loop Playback", "bgmVolume": "Background Music Volume"}, "errors": {"no_audio_file": "Please select an audio file", "invalid_volume": "Invalid volume value", "invalid_volume_number": "Please enter a valid volume number"}}, "replace_audio": {"name": "Replace Audio", "description": "Replace video audio track", "params": {"audioFile": "Audio File", "loopAudio": "Loop Playback", "audioVolume": "Audio Volume"}, "errors": {"no_audio_file": "Please select an audio file", "invalid_volume": "Invalid volume value", "invalid_volume_number": "Please enter a valid volume number"}}, "mute_audio": {"name": "Mute Audio", "description": "Remove video audio", "params": {}, "errors": {}}, "flip_horizontal": {"name": "<PERSON><PERSON>", "description": "Flip video horizontally", "params": {}, "errors": {}}, "flip_vertical": {"name": "Flip Vertical", "description": "Flip video vertically", "params": {}, "errors": {}}, "crop_video": {"name": "Crop Video", "description": "Crop video frame size", "params": {"x": "X Coordinate", "y": "Y Coordinate", "width": "<PERSON><PERSON><PERSON>", "height": "Height"}, "errors": {"negative_x": "X coordinate cannot be negative", "negative_y": "Y coordinate cannot be negative", "invalid_width": "Width must be greater than 0", "invalid_height": "Height must be greater than 0", "invalid_x_number": "Please enter a valid X coordinate number", "invalid_y_number": "Please enter a valid Y coordinate number", "invalid_width_number": "Please enter a valid width number", "invalid_height_number": "Please enter a valid height number"}}, "video_watermark": {"name": "Video Watermark", "description": "Add video watermark", "params": {"watermarkPath": "Watermark Video", "position": "Position", "width": "<PERSON><PERSON><PERSON>", "opacity": "Opacity", "customX": "Custom X Coordinate", "customY": "Custom Y Coordinate"}, "errors": {"no_watermark_file": "Please select watermark video", "invalid_width": "Width must be between 10 and 1000", "invalid_opacity": "Opacity must be between 0.1 and 1.0", "invalid_width_number": "Please enter a valid width number", "invalid_opacity_number": "Please enter a valid opacity number"}}, "batch_image_to_video": {"name": "Batch Images to Video", "description": "Convert multiple images to video", "params": {"durationMode": "Duration Mode", "durationValue": "Duration Value", "resolution": "Resolution", "customWidth": "Custom Width", "customHeight": "Custom Height", "transitionType": "Transition Effect", "transitionDuration": "Transition Duration"}, "errors": {"invalid_duration": "Duration value must be between 0.1 and 300.0 seconds", "invalid_transition_duration": "Transition duration must be between 0.1 and 5.0 seconds", "invalid_duration_number": "Please enter a valid duration number", "invalid_transition_duration_number": "Please enter a valid transition duration number", "invalid_width": "Width must be between 64 and 7680", "invalid_height": "Height must be between 64 and 4320", "invalid_width_number": "Please enter a valid width number", "invalid_height_number": "Please enter a valid height number"}}, "video_to_image": {"name": "Video to Images", "description": "Extract images from video", "params": {"count": "Image Count", "format": "Image Format", "method": "Extraction Method", "quality": "Image Quality", "useCustomSize": "Custom Size", "customWidth": "Custom Width (height auto-adjusted)"}, "errors": {"invalid_count": "Image count must be between 1 and 1000", "invalid_quality": "Image quality must be between 1 and 100"}}}, "formats": {"video": {"mp4": "MP4", "avi": "AVI", "mov": "MOV", "mkv": "MKV", "webm": "WebM"}, "audio": {"mp3": "MP3", "wav": "WAV", "aac": "AAC", "flac": "FLAC", "ogg": "OGG", "m4a": "M4A"}, "image": {"jpg": "JPG", "png": "PNG", "webp": "WebP", "gif": "GIF"}}, "resolutions": {"480p": "480p (854×480)", "720p": "720p (1280×720)", "1080p": "1080p (1920×1080)", "1440p": "1440p (2560×1440)", "4k": "4K (3840×2160)", "custom": "Custom"}, "positions": {"top_left": "Top Left", "top_right": "Top Right", "bottom_left": "Bottom Left", "bottom_right": "Bottom Right", "center": "Center", "custom": "Custom"}, "duration_modes": {"per_image": "Per Image", "total": "Total Duration"}, "transitions": {"none": "None", "fade": "Fade", "slideLeft": "Slide Left", "slideRight": "Slide Right"}, "extraction_methods": {"uniform": "Uniform", "random": "Random"}, "bitrates": {"low": "500 kbps (Low Quality)", "standard": "1000 kbps (Standard)", "high": "2000 kbps (High Quality)", "ultra": "4000 kbps (Ultra Quality)", "bluray": "8000 kbps (Blu-ray Quality)", "custom": "Custom"}, "ui": {"action_list": {"search_placeholder": "Search actions...", "search_results": "Search Results", "no_results": "No matching actions found", "category": "Category", "added": "Added", "theme": "Theme"}, "action_card": {"description": "Description", "description_placeholder": "Action description...", "segments": "Segments", "no_segments": "No segments", "segments_hint": "Please use preview to import time segments", "segments_imported": "Imported from preview: {count} segments", "time_overlap": "Time overlap detected", "delete_all": "Delete All", "validation_errors": "Parameter validation errors:"}, "processing": {"title": "Processing...", "cancelled": "Processing cancelled", "completed": "All actions completed!", "progress": "Progress"}, "language": {"switch": "Switch Language", "current": "Current Language"}}, "messages": {"file_select_error": "File selection failed", "validation_failed": "Parameter validation failed", "action_added": "Action added", "action_removed": "Action removed", "language_switched": "Language switched to {language}"}}